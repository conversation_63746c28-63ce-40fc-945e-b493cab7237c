package com.swhd.agent.service.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourcePageParam;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeSource;

/**
 * 自助充值源表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface AccountOceanengineCustomerRechargeSourceService extends IBaseHdService<AccountOceanengineCustomerRechargeSource> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<AccountOceanengineCustomerRechargeSource> page(AccountOceanengineCustomerRechargeSourcePageParam param);

}
