package com.swhd.agent.service.account.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceAddParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourcePageParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeSourceResult;
import com.swhd.agent.api.account.client.AccountOceanengineCustomerRechargeSourceClient;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeSource;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeSourceService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping(AccountOceanengineCustomerRechargeSourceClient.BASE_PATH)
public class AccountOceanengineCustomerRechargeSourceController implements AccountOceanengineCustomerRechargeSourceClient {

    private final AccountOceanengineCustomerRechargeSourceService accountOceanengineCustomerRechargeSourceService;

    @Override
    public Rsp<PageResult<AccountOceanengineCustomerRechargeSourceResult>> page(AccountOceanengineCustomerRechargeSourcePageParam param) {
        IPage<AccountOceanengineCustomerRechargeSource> iPage = accountOceanengineCustomerRechargeSourceService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AccountOceanengineCustomerRechargeSourceResult.class));
    }

    @Override
    public Rsp<AccountOceanengineCustomerRechargeSourceResult> getById(Long id) {
        AccountOceanengineCustomerRechargeSource entity = accountOceanengineCustomerRechargeSourceService.getById(id);
        return RspHd.data(Func.copy(entity, AccountOceanengineCustomerRechargeSourceResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineCustomerRechargeSourceResult>> listByIds(Collection<Long> ids) {
        List<AccountOceanengineCustomerRechargeSource> list = accountOceanengineCustomerRechargeSourceService.listByIds(ids);
        return RspHd.data(Func.copy(list, AccountOceanengineCustomerRechargeSourceResult.class));
    }

    @Override
    public Rsp<Void> add(AccountOceanengineCustomerRechargeSourceAddParam param) {
        boolean result = accountOceanengineCustomerRechargeSourceService.save(Func.copy(param, AccountOceanengineCustomerRechargeSource.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(AccountOceanengineCustomerRechargeSourceUpdateParam param) {
        boolean result = accountOceanengineCustomerRechargeSourceService.updateById(Func.copy(param, AccountOceanengineCustomerRechargeSource.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = accountOceanengineCustomerRechargeSourceService.removeByIds(ids);
        return RspHd.status(result);
    }

}
