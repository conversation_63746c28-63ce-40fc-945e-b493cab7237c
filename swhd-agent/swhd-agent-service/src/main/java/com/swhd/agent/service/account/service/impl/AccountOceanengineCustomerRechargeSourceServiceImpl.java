package com.swhd.agent.service.account.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourcePageParam;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeSource;
import com.swhd.agent.service.account.mapper.AccountOceanengineCustomerRechargeSourceMapper;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeSourceService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 自助充值源表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerRechargeSourceServiceImpl extends BaseHdServiceImpl<AccountOceanengineCustomerRechargeSourceMapper, AccountOceanengineCustomerRechargeSource> implements AccountOceanengineCustomerRechargeSourceService {

    @Override
    public IPage<AccountOceanengineCustomerRechargeSource> page(AccountOceanengineCustomerRechargeSourcePageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getTitle()), AccountOceanengineCustomerRechargeSource::getTitle, param.getTitle())
                .eq(Func.isNotEmpty(param.getCustomerIds()), AccountOceanengineCustomerRechargeSource::getCustomerIds, param.getCustomerIds())
                .eq(Func.isNotEmpty(param.getState()), AccountOceanengineCustomerRechargeSource::getState, param.getState())
                .orderByDesc(AccountOceanengineCustomerRechargeSource::getCreateTime)
                .orderByDesc(AccountOceanengineCustomerRechargeSource::getId)
                .page(convertToPage(param));
    }

}
